#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试心跳同步管理器的一次性同步功能
验证当 is_heart_rate = FALSE 时的行为
"""

import sys
import time
from unittest.mock import Mock, MagicMock
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加项目路径
sys.path.append('.')

from utils.heart_rate_sync_manager import HeartRateSyncManager


class TestHeartRateSyncOneTime:
    """测试心跳同步管理器的一次性同步功能"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.setup_mocks()
        
    def setup_mocks(self):
        """设置模拟对象"""
        # 模拟配置管理器
        self.mock_config_manager = Mock()
        self.mock_config_manager.sync_from_supabase = Mock(return_value=True)
        self.mock_config_manager.sync_heart_rate_settings = Mock(return_value=True)
        self.mock_config_manager.sync_table_mode = Mock(return_value=True)
        self.mock_config_manager.sync_fuzzy_query = Mock(return_value=True)
        self.mock_config_manager.sync_db_scan_fast = Mock(return_value=True)
        self.mock_config_manager.sync_test_mode_settings = Mock(return_value=True)
        
        # 模拟Supabase辅助类
        self.mock_supabase_helper = Mock()
        self.mock_supabase_helper.is_connected = Mock(return_value=True)
        self.mock_supabase_helper.is_authenticated = Mock(return_value=True)
        
        # 模拟日志回调
        self.log_messages = []
        self.log_callback = lambda msg: self.log_messages.append(msg)
        
    def test_one_time_sync_disabled(self):
        """测试禁用心跳同步时的一次性同步"""
        print("=== 测试一次性同步（is_heart_rate = FALSE）===")
        
        # 设置心跳同步为禁用
        self.mock_config_manager.get_heart_rate_settings = Mock(return_value={
            'is_heart_rate': False,
            'heart_rate_time': 300
        })
        
        # 创建心跳同步管理器
        sync_manager = HeartRateSyncManager(
            config_manager=self.mock_config_manager,
            supabase_helper=self.mock_supabase_helper,
            log_callback=self.log_callback
        )
        
        # 连接信号以监控同步过程
        sync_started = False
        sync_completed = False
        sync_success = False
        
        def on_sync_started():
            nonlocal sync_started
            sync_started = True
            print("✅ 同步开始信号已触发")
            
        def on_sync_completed(success):
            nonlocal sync_completed, sync_success
            sync_completed = True
            sync_success = success
            print(f"✅ 同步完成信号已触发，成功: {success}")
            
        sync_manager.sync_started.connect(on_sync_started)
        sync_manager.sync_completed.connect(on_sync_completed)
        
        # 初始化（应该执行一次性同步）
        print("初始化心跳同步管理器...")
        success = sync_manager.initialize()
        
        # 验证初始化成功
        assert success, "初始化应该成功"
        print("✅ 初始化成功")
        
        # 获取状态
        status = sync_manager.get_status()
        print(f"状态: {status}")
        
        # 验证状态
        assert not status['is_enabled'], "心跳同步应该被禁用"
        assert not status['timer_active'], "定时器应该不活跃"
        print("✅ 状态验证通过：心跳同步已禁用，定时器未启动")
        
        # 等待一次性同步完成（延迟2秒执行）
        print("等待一次性同步完成...")
        start_time = time.time()
        while not sync_completed and (time.time() - start_time) < 5:
            self.app.processEvents()
            time.sleep(0.1)
        
        # 验证同步执行
        assert sync_started, "应该触发同步开始信号"
        assert sync_completed, "应该触发同步完成信号"
        assert sync_success, "一次性同步应该成功"
        print("✅ 一次性同步执行成功")
        
        # 验证同步操作被调用
        self.mock_config_manager.sync_from_supabase.assert_called_once()
        self.mock_config_manager.sync_heart_rate_settings.assert_called_once()
        print("✅ 同步操作已正确调用")
        
        # 验证日志消息
        log_text = ' '.join(self.log_messages)
        assert "一次性参数同步" in log_text, "应该包含一次性同步的日志"
        print("✅ 日志消息验证通过")
        
        print("=== 一次性同步测试完成 ===\n")
        
    def test_regular_sync_enabled(self):
        """测试启用心跳同步时的定期同步"""
        print("=== 测试定期同步（is_heart_rate = TRUE）===")
        
        # 清理之前的日志
        self.log_messages.clear()
        
        # 设置心跳同步为启用
        self.mock_config_manager.get_heart_rate_settings = Mock(return_value={
            'is_heart_rate': True,
            'heart_rate_time': 60  # 1分钟间隔
        })
        
        # 重置模拟对象的调用计数
        self.mock_config_manager.sync_from_supabase.reset_mock()
        self.mock_config_manager.sync_heart_rate_settings.reset_mock()
        
        # 创建心跳同步管理器
        sync_manager = HeartRateSyncManager(
            config_manager=self.mock_config_manager,
            supabase_helper=self.mock_supabase_helper,
            log_callback=self.log_callback
        )
        
        # 初始化（应该启动定期同步）
        print("初始化心跳同步管理器...")
        success = sync_manager.initialize()
        
        # 验证初始化成功
        assert success, "初始化应该成功"
        print("✅ 初始化成功")
        
        # 获取状态
        status = sync_manager.get_status()
        print(f"状态: {status}")
        
        # 验证状态
        assert status['is_enabled'], "心跳同步应该被启用"
        assert status['timer_active'], "定时器应该活跃"
        assert status['interval_seconds'] == 60, "间隔时间应该是60秒"
        print("✅ 状态验证通过：心跳同步已启用，定时器已启动")
        
        # 验证日志消息
        log_text = ' '.join(self.log_messages)
        assert "心跳同步已启动" in log_text, "应该包含心跳同步启动的日志"
        print("✅ 日志消息验证通过")
        
        # 停止同步管理器
        sync_manager.stop()
        print("✅ 同步管理器已停止")
        
        print("=== 定期同步测试完成 ===\n")
        
    def test_sync_mode_switching(self):
        """测试同步模式切换"""
        print("=== 测试同步模式切换 ===")
        
        # 清理之前的日志
        self.log_messages.clear()
        
        # 初始设置为启用
        heart_rate_settings = {'is_heart_rate': True, 'heart_rate_time': 300}
        self.mock_config_manager.get_heart_rate_settings = Mock(return_value=heart_rate_settings)
        
        # 创建心跳同步管理器
        sync_manager = HeartRateSyncManager(
            config_manager=self.mock_config_manager,
            supabase_helper=self.mock_supabase_helper,
            log_callback=self.log_callback
        )
        
        # 初始化为启用状态
        sync_manager.initialize()
        status = sync_manager.get_status()
        assert status['is_enabled'], "初始状态应该启用"
        assert status['timer_active'], "定时器应该活跃"
        print("✅ 初始状态：心跳同步已启用")
        
        # 切换到禁用状态
        heart_rate_settings['is_heart_rate'] = False
        sync_manager.update_settings()
        
        status = sync_manager.get_status()
        assert not status['is_enabled'], "切换后应该禁用"
        assert not status['timer_active'], "定时器应该不活跃"
        print("✅ 切换后状态：心跳同步已禁用")
        
        # 验证日志包含禁用信息
        log_text = ' '.join(self.log_messages)
        assert "心跳同步已禁用" in log_text, "应该包含禁用的日志"
        print("✅ 模式切换测试完成")
        
        print("=== 同步模式切换测试完成 ===\n")
        
    def run_all_tests(self):
        """运行所有测试"""
        print("开始测试心跳同步管理器的一次性同步功能...\n")
        
        try:
            self.test_one_time_sync_disabled()
            self.test_regular_sync_enabled()
            self.test_sync_mode_switching()
            
            print("🎉 所有测试通过！")
            print("\n测试总结：")
            print("✅ 一次性同步功能正常工作")
            print("✅ 定期同步功能正常工作")
            print("✅ 同步模式切换功能正常工作")
            print("✅ 状态管理功能正常工作")
            print("✅ 日志记录功能正常工作")
            
        except AssertionError as e:
            print(f"❌ 测试失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 测试出错: {e}")
            return False
            
        return True


if __name__ == "__main__":
    # 运行测试
    test = TestHeartRateSyncOneTime()
    success = test.run_all_tests()
    
    # 退出应用
    sys.exit(0 if success else 1)
